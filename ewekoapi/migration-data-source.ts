import { DataSource } from 'typeorm';
import { User } from './src/users/entities/user.entity';
import { Farmer } from './src/users/entities/farmers/farmer.entity';
import { Buyer } from './src/users/entities/buyers/buyer.entity';
import { Admin } from './src/users/entities/admins/admin.entity';
import { PhoneNumber } from './src/users/entities/phone-numbers/phone-number.entity';
import { Address } from './src/addresses/entities/address.entity';
import { Otp } from './src/otp/entities/otp.entity';
import { Cart, CartItem } from './src/cart/entities/cart.entity';
import { Produce } from './src/produce/entities/produce.entity';
import { Category } from './src/category/entities/category.entity';
import { Order, OrderItem } from './src/orders/entities/order.entity';
import { Transaction } from './src/transactions/entities/transaction.entity';
import { Wallet } from './src/wallets/entities/wallet.entity';
import { Preferences } from './src/preferences/entities/preferences.entity';
import { Notification } from './src/notifications/entities/notification.entity';
import { AdminToken } from './src/admin-tokens/entities/admin-token.entity';
import { WeeklyPrice } from './src/weekly-price/entities/weekly-price.entity';

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: Number(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'eweko_db',
  entities: [
    User,
    Farmer,
    Buyer,
    Admin,
    PhoneNumber,
    Address,
    Otp,
    Cart,
    CartItem,
    Produce,
    Category,
    Order,
    OrderItem,
    Transaction,
    Wallet,
    Preferences,
    Notification,
    AdminToken,
    WeeklyPrice,
  ],
  migrations: ['src/migration/*{.ts,.js}'],
  synchronize: false,
  logging: true,
});
