import { Modu<PERSON> } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Buyer } from '../users/entities/buyers/buyer.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { Admin } from '../users/entities/admins/admin.entity';
import { PhoneNumber } from '../users/entities/phone-numbers/phone-number.entity';
import { Order, OrderItem } from './entities/order.entity';
import { Produce } from '../produce/entities/produce.entity';
import { UsersService } from 'src/users/users.service';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Buyer, Farmer, Admin, PhoneNumber, Order, OrderItem, Produce]),
  ],
  controllers: [OrdersController],
  providers: [OrdersService, UsersService, PaginationService],
  exports: [OrdersService, TypeOrmModule],
})
export class OrdersModule {}
