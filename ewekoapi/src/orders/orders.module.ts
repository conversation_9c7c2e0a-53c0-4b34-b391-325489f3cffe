import { Modu<PERSON> } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User }
import { Buyer, Farmer, Admin, PhoneNumber } from '../users/entities/user.entity';
import { Order, OrderItem } from './entities/order.entity';
import { Produce } from '../produce/entities/produce.entity';
import { UsersService } from 'src/users/users.service';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Buyer, Farmer, Admin, PhoneNumber, Order, OrderItem, Produce]),
  ],
  controllers: [OrdersController],
  providers: [OrdersService, UsersService, PaginationService],
  exports: [OrdersService, TypeOrmModule],
})
export class OrdersModule {}
