import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateWalletDto } from './dto/create-wallet.dto';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Wallet } from './entities/wallet.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';

@Injectable()
export class WalletsService {
  constructor(
    @InjectRepository(Wallet)
    private walletRepository: Repository<Wallet>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createWalletDto: CreateWalletDto): Promise<Wallet> {
    const { farmer } = createWalletDto;

    // Create new wallet
    const wallet = this.walletRepository.create({
      farmer_id: farmer,
      balance: 0,
    });

    return await this.walletRepository.save(wallet);
  }

  // async findAll(): Promise<Wallet[]> {
  //   return this.walletModel.find().populate(['farmer']).exec();
  // }

  async findAll() {
    const wallets = await this.walletRepository.find({
      relations: ['farmer'],
      order: { created_at: 'DESC' }
    });

    return wallets;
  }

  async findByFarmer(farmerId: string): Promise<Wallet> {
    // First check if the farmer exists
    const farmerExists = await this.farmerRepository.findOne({
      where: { user_id: farmerId }
    });
    if (!farmerExists) {
      throw new NotFoundException('Farmer not found');
    }

    const wallet = await this.walletRepository.findOne({
      where: { farmer_id: farmerId },
      relations: ['farmer']
    });

    if (!wallet) {
      throw new NotFoundException('Wallet not found for this farmer');
    }

    return wallet;
  }

  async findOne(id: string): Promise<Wallet> {
    const wallet = await this.walletRepository.findOne({
      where: { id },
      relations: ['farmer']
    });

    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    return wallet;
  }

  async update(id: string, updateWalletDto: UpdateWalletDto) {
    const result = await this.walletRepository.update(id, updateWalletDto);

    if (result.affected === 0) {
      throw new NotFoundException('Wallet not found');
    }

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.walletRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException('Wallet not found');
    }
  }
}
