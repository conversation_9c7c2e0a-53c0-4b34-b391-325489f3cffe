import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AdminTokensService } from './admin-tokens.service';
import { AdminTokensController } from './admin-tokens.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Admin } from '../users/entities/admins/admin.entity';
import { AdminToken } from './entities/admin-token.entity';
import { Buyer } from '../users/entities/buyers/buyer.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { PhoneNumber } from '../users/entities/phone-numbers/phone-number.entity';
import { UsersService } from 'src/users/users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Admin, AdminToken, Buyer, Farmer, PhoneNumber]),
  ],
  controllers: [AdminTokensController],
  providers: [AdminTokensService, UsersService],
  exports: [AdminTokensService, TypeOrmModule],
})
export class AdminTokensModule {}
