// Main User entity
export { User } from './user.entity';

// User type-specific entities
export { Farmer } from './farmers/farmer.entity';
export { Buyer } from './buyers/buyer.entity';
export { Admin } from './admins/admin.entity';

// User-related entities
export { PhoneNumber } from './phone-numbers/phone-number.entity';

// Re-export entities from other modules for convenience
export { Address } from '../../addresses/entities/address.entity';
export { Notification } from '../../notifications/entities/notification.entity';
export { Otp } from '../../otp/entities/otp.entity';
export { Cart, CartItem } from '../../cart/entities/cart.entity';
export { Category } from '../../category/entities/category.entity';
export { Produce } from '../../produce/entities/produce.entity';
export { Order, OrderItem } from '../../orders/entities/order.entity';
export { Transaction } from '../../transactions/entities/transaction.entity';
export { Wallet } from '../../wallets/entities/wallet.entity';
export { Preferences } from '../../preferences/entities/preferences.entity';
export { AdminToken } from '../../admin-tokens/entities/admin-token.entity';
export { WeeklyPrice } from '../../weekly-price/entities/weekly-price.entity';
