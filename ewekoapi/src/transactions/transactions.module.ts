import { Module } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { TransactionsController } from './transactions.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Buyer, Farmer, Admin, PhoneNumber } from '../users/entities/user.entity';
import { Transaction } from './entities/transaction.entity';
import { Order } from '../orders/entities/order.entity';
import { Produce } from '../produce/entities/produce.entity';
import { UsersService } from 'src/users/users.service';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Buyer, Farmer, Admin, PhoneNumber, Transaction, Order, Produce]),
  ],
  controllers: [TransactionsController],
  providers: [TransactionsService, UsersService, PaginationService],
  exports: [TransactionsService, TypeOrmModule],
})
export class TransactionsModule {}
