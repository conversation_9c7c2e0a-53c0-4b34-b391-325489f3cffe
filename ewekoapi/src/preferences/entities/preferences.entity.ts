import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('preferences')
export class Preferences {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ default: true })
  receive_promotions: boolean;

  @Column({ default: false })
  enable_2fa: boolean;

  @Column({ default: true })
  general_updates: boolean;

  @Column({ default: true })
  order_updates: boolean;

  @Column({ default: true })
  transaction_updates: boolean;

  @Column({ default: true })
  payment_updates: boolean;

  @Column({ default: true })
  delivery_updates: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @OneToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
