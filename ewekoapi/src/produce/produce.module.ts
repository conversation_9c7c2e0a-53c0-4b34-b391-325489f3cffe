import { Module } from '@nestjs/common';
import { ProduceService } from './produce.service';
import { ProduceController } from './produce.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User }
import { Farmer, Category } from '../users/entities/user.entity';
import { Produce } from './entities/produce.entity';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Farmer, Produce, Category]),
  ],
  controllers: [ProduceController],
  providers: [ProduceService, PaginationService],
  exports: [ProduceService, TypeOrmModule],
})
export class ProduceModule {}
