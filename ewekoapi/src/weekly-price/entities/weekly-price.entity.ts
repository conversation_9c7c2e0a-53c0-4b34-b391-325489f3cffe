import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('weekly_prices')
export class WeeklyPrice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  category_id: string;

  @Column()
  produce_name: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  average_price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  min_price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  max_price: number;

  @Column({ type: 'int' })
  week_number: number;

  @Column({ type: 'int' })
  year: number;

  @Column({ type: 'date' })
  week_start_date: Date;

  @Column({ type: 'date' })
  week_end_date: Date;

  @Column({ type: 'json', nullable: true })
  market_data: any;

  @Column({ default: true })
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships - using forward reference to avoid circular dependency
  @ManyToOne('Category', 'weekly_prices')
  @JoinColumn({ name: 'category_id' })
  category: any;
}
