#!/bin/bash

# Fix all entity imports across the codebase

# Fix User entity imports (keep as is)
# Fix Farmer entity imports
find src -name "*.ts" -exec sed -i '' 's/import { User, Farmer/import { User }\nimport { Farmer/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Farmer } from.*users\/entities\/user\.entity/import { Farmer } from "..\/users\/entities\/farmers\/farmer.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Farmer } from.*\.\.\/users\/entities\/user\.entity/import { Farmer } from "..\/users\/entities\/farmers\/farmer.entity"/g' {} \;

# Fix Buyer entity imports
find src -name "*.ts" -exec sed -i '' 's/import { User, Buyer/import { User }\nimport { Buyer/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Buyer } from.*users\/entities\/user\.entity/import { Buyer } from "..\/users\/entities\/buyers\/buyer.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Buyer } from.*\.\.\/users\/entities\/user\.entity/import { Buyer } from "..\/users\/entities\/buyers\/buyer.entity"/g' {} \;

# Fix Admin entity imports
find src -name "*.ts" -exec sed -i '' 's/import { User, Admin/import { User }\nimport { Admin/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Admin } from.*users\/entities\/user\.entity/import { Admin } from "..\/users\/entities\/admins\/admin.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Admin } from.*\.\.\/users\/entities\/user\.entity/import { Admin } from "..\/users\/entities\/admins\/admin.entity"/g' {} \;

# Fix PhoneNumber entity imports
find src -name "*.ts" -exec sed -i '' 's/import { User, PhoneNumber/import { User }\nimport { PhoneNumber/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { PhoneNumber } from.*users\/entities\/user\.entity/import { PhoneNumber } from "..\/users\/entities\/phone-numbers\/phone-number.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { PhoneNumber } from.*\.\.\/users\/entities\/user\.entity/import { PhoneNumber } from "..\/users\/entities\/phone-numbers\/phone-number.entity"/g' {} \;

# Fix Address entity imports
find src -name "*.ts" -exec sed -i '' 's/import { User, Address/import { User }\nimport { Address/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Address } from.*users\/entities\/user\.entity/import { Address } from "..\/addresses\/entities\/address.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Address } from.*\.\.\/users\/entities\/user\.entity/import { Address } from "..\/addresses\/entities\/address.entity"/g' {} \;

# Fix Notification entity imports
find src -name "*.ts" -exec sed -i '' 's/import { User, Notification/import { User }\nimport { Notification/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Notification } from.*users\/entities\/user\.entity/import { Notification } from "..\/notifications\/entities\/notification.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Notification } from.*\.\.\/users\/entities\/user\.entity/import { Notification } from "..\/notifications\/entities\/notification.entity"/g' {} \;

# Fix Otp entity imports
find src -name "*.ts" -exec sed -i '' 's/import { Otp } from.*users\/entities\/user\.entity/import { Otp } from "..\/otp\/entities\/otp.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Otp } from.*\.\.\/users\/entities\/user\.entity/import { Otp } from "..\/otp\/entities\/otp.entity"/g' {} \;

# Fix other entity imports
find src -name "*.ts" -exec sed -i '' 's/import { Category } from.*users\/entities\/user\.entity/import { Category } from "..\/category\/entities\/category.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Wallet } from.*users\/entities\/user\.entity/import { Wallet } from "..\/wallets\/entities\/wallet.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { Preferences } from.*users\/entities\/user\.entity/import { Preferences } from "..\/preferences\/entities\/preferences.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { AdminToken } from.*users\/entities\/user\.entity/import { AdminToken } from "..\/admin-tokens\/entities\/admin-token.entity"/g' {} \;
find src -name "*.ts" -exec sed -i '' 's/import { WeeklyCategoryPrice } from.*users\/entities\/user\.entity/import { WeeklyPrice } from "..\/weekly-price\/entities\/weekly-price.entity"/g' {} \;

echo "Import fixes completed!"
